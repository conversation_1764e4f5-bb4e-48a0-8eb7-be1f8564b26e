package physique;

public class Position {
    private int i, j;

    public Position(int i, int j) {
        this.i = i;
        this.j = j;
    }

    public Position(Position other) {
        this.i = other.i;
        this.j = other.j;
    }

    public int getI() { return i; }
    public int getJ() { return j; }

    public void setI(int i) { this.i = i; }
    public void setJ(int j) { this.j = j; }

    public Position clone() {
        return new Position(this);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof Position)) return false;
        Position other = (Position) obj;
        return this.i == other.i && this.j == other.j;
    }

    public void additionnerPos(Position pos) {
        this.i += pos.i;
        this.j += pos.j;
    }

    public void soustrairePos(Position pos) {
        this.i -= pos.i;
        this.j -= pos.j;
    }

    public void multiplierPos(double posi, double posj) {
        this.i *= posi;
        this.j *= posj;
    }

    @Override
    public String toString() {
        return "{" + i + "," + j + "}";
    }
}