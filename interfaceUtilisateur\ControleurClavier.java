package interfaceUtilisateur;

/**
 * Cette classe est un listener pour les événements clavier utilisés pour contrôler
 * le héros dans le jeu.
 * 
 * Ces méthodes héritées ne sont pas utilisées :
 *	public void keyReleased(KeyEvent e) {}
 *  public void keyTyped(KeyEvent e) {}
 * 
 * Mise à jour : contrôle du héros via flèches directionnelles.
 * 
 * <AUTHOR> | ETS
 * @version Hiver 2022 - TP2
 */

import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

import modele.PlanDeJeu;
import personnage.Heros;
import physique.Direction;

public class ControleurClavier implements KeyListener {

	// Référence au plan de jeu
	private PlanDeJeu planDeJeu;

	/**
	 * Attache une instance du plan de jeu au contrôleur
	 * @param planDeJeu instance du modèle
	 */
	public void attacherPlanDeJeu(PlanDeJeu planDeJeu) {
		this.planDeJeu = planDeJeu;
	}

	/**
	 * Gestionnaire d’événement pour les flèches clavier
	 */
	@Override
	public void keyPressed(KeyEvent e) {
		if (planDeJeu == null || planDeJeu.getHeros() == null) return;

		Heros heros = planDeJeu.getHeros();

		switch (e.getKeyCode()) {
			case KeyEvent.VK_UP:
				heros.seDeplacer(Direction.HAUT);
				break;
			case KeyEvent.VK_DOWN:
				heros.seDeplacer(Direction.BAS);
				break;
			case KeyEvent.VK_LEFT:
				heros.seDeplacer(Direction.GAUCHE);
				break;
			case KeyEvent.VK_RIGHT:
				heros.seDeplacer(Direction.DROITE);
				break;
			default:
				break;
		}

		// Mise à jour de la vue
		planDeJeu.avertir();
	}

	@Override
	public void keyReleased(KeyEvent e) {}

	@Override
	public void keyTyped(KeyEvent e) {}
}
