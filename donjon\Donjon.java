package donjon;

import physique.*;
import pile.PileSChainee;
import java.util.Random;
import java.util.ArrayList;

public class Donjon {
    private Case[][] grille;
    private Case depart, fin;
    private Random rand;

    public Donjon() {
        Configuration config = Configuration.getInstance();
        grille = new Case[config.getConfig(Configuration.NB_LIGNES)][config.getConfig(Configuration.NB_COLONNES)];
        rand = new Random();

        // Création des cases
        for (int i = 0; i < config.getConfig(Configuration.NB_LIGNES); i++) {
            for (int j = 0; j < config.getConfig(Configuration.NB_COLONNES); j++) {
                grille[i][j] = new Case(new Position(i, j));
            }
        }

        // Position de départ aléatoire
        Position posDepart = getPositionAlea();
        depart = grille[posDepart.getI()][posDepart.getJ()];

        // Génération du labyrinthe
        produireLabyrinthe();

        // Définir la case de fin uniquement si elle existe
        if (fin != null) {
            fin.setFin();
        }
    }

    public Case getDepart() {
        return depart;
    }

    public Case getFin() {
        return fin;
    }

    public Case[][] getGrille() {
        return grille;
    }

    public Position getPositionAlea() {
        Configuration config = Configuration.getInstance();
        int i = rand.nextInt(config.getConfig(Configuration.NB_LIGNES));
        int j = rand.nextInt(config.getConfig(Configuration.NB_COLONNES));
        return new Position(i, j);
    }

    public int getNbVoisinsNonDeveloppe(Position pos) {
        int count = 0;
        Direction dir = new Direction();

        for (int d = 0; d < 4; d++) {
            Position dirPos = dir.directionAPosition(d);
            Position voisinPos = new Position(pos);
            voisinPos.additionnerPos(dirPos);

            if (estValide(voisinPos)) {
                if (!grille[voisinPos.getI()][voisinPos.getJ()].getDeveloppe()) {
                    count++;
                }
            }
        }

        return count;
    }

    private boolean estValide(Position pos) {
        Configuration config = Configuration.getInstance();
        return pos.getI() >= 0 && pos.getI() < config.getConfig(Configuration.NB_LIGNES)
            && pos.getJ() >= 0 && pos.getJ() < config.getConfig(Configuration.NB_COLONNES);
    }

    public Position getVoisinAlea(Position pos) {
        Direction dir = new Direction();
        ArrayList<Position> voisins = new ArrayList<>();

        for (int d = 0; d < 4; d++) {
            Position dirPos = dir.directionAPosition(d);
            Position voisinPos = new Position(pos);
            voisinPos.additionnerPos(dirPos);

            if (estValide(voisinPos)) {
                voisins.add(voisinPos);
            }
        }

        if (voisins.isEmpty()) return null;

        return voisins.get(rand.nextInt(voisins.size()));
    }

    public Position getVoisinLibreAlea(Position pos) {
        Direction dir = new Direction();
        ArrayList<Position> libres = new ArrayList<>();

        for (int d = 0; d < 4; d++) {
            Position dirPos = dir.directionAPosition(d);
            Position voisinPos = new Position(pos);
            voisinPos.additionnerPos(dirPos);

            if (estValide(voisinPos)) {
                Case voisinCase = grille[voisinPos.getI()][voisinPos.getJ()];
                if (!voisinCase.getDeveloppe()) {
                    libres.add(voisinPos);
                }
            }
        }

        if (libres.isEmpty()) return null;

        return libres.get(rand.nextInt(libres.size()));
    }

    public void produireLabyrinthe() {
        PileSChainee<Case> pile = new PileSChainee<>();
        pile.empiler(depart);

        Direction direction = new Direction();

        while (!pile.estVide()) {
            Case courante = pile.regarder();
            Position pos = courante.getPosition();
            courante.setDeveloppe(true);

            if (getNbVoisinsNonDeveloppe(pos) > 0) {
                Position voisinPos = getVoisinLibreAlea(pos);
                if (voisinPos == null) {
                    pile.depiler();
                    continue;
                }

                Case voisin = grille[voisinPos.getI()][voisinPos.getJ()];

                Position vecteur = new Position(voisinPos);
                vecteur.soustrairePos(pos);
                int dir = direction.positionADirection(vecteur);

                courante.setVoisin(dir, voisin);
                voisin.setVoisin(direction.directionOpposee(dir), courante);

                pile.empiler(voisin);
                fin = voisin; // La dernière case empilée devient la fin
            } else {
                pile.depiler();
            }
        }
    }
}
