Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA21730000 ntdll.dll
7FFA20800000 KERNEL32.DLL
7FFA1E8C0000 KERNELBASE.dll
7FFA1F990000 USER32.dll
7FFA1EF40000 win32u.dll
7FFA209F0000 GDI32.dll
7FFA1ECA0000 gdi32full.dll
7FFA1E820000 msvcp_win.dll
7FFA1F2A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA20A40000 advapi32.dll
7FFA20700000 msvcrt.dll
7FFA20B60000 sechost.dll
7FFA1EDD0000 bcrypt.dll
7FFA1F3C0000 RPCRT4.dll
7FFA1DF40000 CRYPTBASE.DLL
7FFA1F160000 bcryptPrimitives.dll
7FFA207B0000 IMM32.DLL
