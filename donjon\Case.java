package donjon;

import physique.Position;

public class Case {
    private Position position;
    private boolean decouverte;
    private boolean fin;
    private boolean developpe;
    private Case[] voisins;

    public Case(Position position) {
        this.position = new Position(position);
        this.voisins = new Case[4]; // HAUT, BAS, GAUCHE, DROITE
    }

    public Position getPosition() {
        return new Position(position);
    }

    public void setDecouverte(boolean decouverte) {
        this.decouverte = decouverte;
    }

    public boolean getDecouverte() {
        return this.decouverte;
    }

    public void setFin() {
        this.fin = true;
    }

    public boolean getFin() {
        return this.fin;
    }

    public void setDeveloppe(boolean developpe) {
        this.developpe = developpe;
    }

    public boolean getDeveloppe() {
        return this.developpe;
    }

    public void setVoisin(int direction, Case voisin) {
        voisins[direction] = voisin;
    }

    public Case getVoisin(int direction) {
        return voisins[direction];
    }

    @Override
    public String toString() {
        return "Case(" + position + ", fin=" + fin + ", decouverte=" + decouverte + ", developpe=" + developpe + ")";
    }
}