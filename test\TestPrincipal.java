import physique.Position;
import physique.Direction;
import pile.PileSChainee;

public class TestPrincipal {
    public static void main(String[] args) {
        System.out.println("=== Test de la classe Position ===");
        Position p1 = new Position(2, 3);
        Position p2 = new Position(1, 1);
        p1.additionnerPos(p2);
        System.out.println("Addition: " + p1);
        p1.soustrairePos(new Position(1, 1));
        System.out.println("Soustraction: " + p1);
        p1.multiplierPos(2.0, 2.0);
        System.out.println("Multiplication: " + p1);
        System.out.println("Clone: " + p1.clone());
        System.out.println("Equals: " + p1.equals(new Position(2, 4)));

        System.out.println("\n=== Test de la classe Direction ===");
        Direction dir = new Direction();
        for (int i = 0; i < 4; i++) {
            Position pos = dir.directionAPosition(i);
            System.out.println("Direction " + i + " -> " + pos);
            System.out.println("Opposé de " + i + ": " + dir.directionOpposee(i));
        }
        System.out.println("Direction aléatoire: " + dir.obtenirDirAlea());

        System.out.println("\n=== Test de la PileSChainee ===");
        PileSChainee<String> pile = new PileSChainee<>();
        pile.empiler("A");
        pile.empiler("B");
        System.out.println("Sommet: " + pile.regarder());
        System.out.println("Dépile: " + pile.depiler());
        System.out.println("Est vide ? " + pile.estVide());
    }
}