package physique;

import java.util.Random;

public class Direction {
    public static final int HAUT = 0;
    public static final int BAS = 1;
    public static final int GAUCHE = 2;
    public static final int DROITE = 3;

    private Random rand = new Random();

    public int directionOpposee(int direction) {
        switch (direction) {
            case HAUT: return BAS;
            case BAS: return HAUT;
            case GAUCHE: return DROITE;
            case DROITE: return GAUCHE;
            default: return -1;
        }
    }

    public Position directionAPosition(int direction) {
        switch (direction) {
            case HAUT: return new Position(-1, 0);
            case BAS: return new Position(1, 0);
            case GAUCHE: return new Position(0, -1);
            case DROITE: return new Position(0, 1);
            default: return new Position(0, 0);
        }
    }

    public int positionADirection(Position pos) {
        if (pos.equals(new Position(-1, 0))) return HAUT;
        if (pos.equals(new Position(1, 0))) return BAS;
        if (pos.equals(new Position(0, -1))) return GAUCHE;
        if (pos.equals(new Position(0, 1))) return DROITE;
        return -1;
    }

    public int obtenirDirAlea() {
        return rand.nextInt(4);
    }
}