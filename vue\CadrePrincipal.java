package vue;

import java.awt.Dimension;
import java.awt.Toolkit;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.JFrame;
import javax.swing.JOptionPane;

import interfaceUtilisateur.ControleurClavier;
import modele.PlanDeJeu;

/**
 * Cadre principal, définit:
 * - un panneau principal
 * - un controleur clavier
 * 
 * définit un comportement de fin de programme par fenêtre de confirmation
 *
 * <AUTHOR>
 * @version Hiver 2022
 */
@SuppressWarnings("serial")
public class CadrePrincipal extends JFrame implements Runnable {
	
	Dimension tailleEcran = Toolkit.getDefaultToolkit().getScreenSize();
	ControleurClavier clavier = new ControleurClavier();
	PlanDeJeu planDeJeu = PlanDeJeu.getInstance();

	/**
	 * Constructeur, initialise la référence au modèle.
	 */
	public CadrePrincipal() {
		super("INF111 - Donjon");
		
		// Enregistre le contrôleur clavier
		this.addKeyListener(clavier);

		// Connecte le plan de jeu au contrôleur
		clavier.attacherPlanDeJeu(planDeJeu);
	}

	/**
	 * Tâche du view-controller
	 */
	@Override
	public void run() {
		
    	// Panneau principal (avec dessin du jeu)
    	PanneauPrincipal panPrincipal = new PanneauPrincipal();
    	
    	// Remplace le ContentPane par notre panneau principal
    	setContentPane(panPrincipal);

    	// Configure la fenêtre
    	configurerFrame();

    	// Donne le focus clavier au cadre
    	setFocusable(true);
    	requestFocusInWindow();

    	// Rend visible
    	setVisible(true);	
	}

	/**
	 * Configure le frame en maximisant la fenêtre, puis
	 * en ajoutant une gestion du exit par pop-up
	 */
	private void configurerFrame() {

    	setExtendedState(JFrame.MAXIMIZED_BOTH);

		// Confirmation avant de quitter
		this.addWindowListener(new WindowAdapter() {
			public void windowClosing(WindowEvent we) {
				int result = JOptionPane.showConfirmDialog(
					null,
					"Do you want to Exit ?",
					"Exit Confirmation : ",
					JOptionPane.YES_NO_OPTION
				);
		        
		        if (result == JOptionPane.YES_OPTION) {
		          setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		        } else {
		          setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
		        }
			}
		});
	}
}
