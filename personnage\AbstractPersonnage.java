package personnage;

/**
 * Classe abstraite d'un personnage d'un jeu
 * 
 * Mise à jour : ajoute gestion observable, déplacements, position synchrone avec la case
 * 
 * <AUTHOR> | ETS
 * @version Hiver 2022 - TP2
 */

import physique.Position;
import donjon.Case;
import observer.MonObservable;

public abstract class AbstractPersonnage extends MonObservable {

	// propriétés d'un personnage
	protected String nom;
	protected Position pos;
	protected Case caseCourante;

	/**
	 * Constructeur
	 * @param nom nom du personnage
	 */
	public AbstractPersonnage(String nom) {
		this.nom = nom;
		this.pos = new Position(0, 0); // Position par défaut
	}

	/**
	 * Déplace le personnage dans la direction indiquée (par code entier)
	 * @param direction entier de 0 à 3
	 */
	public void seDeplacer(int direction) {
		Case voisin = caseCourante.getVoisin(direction);
		if (voisin != null) {
			setCase(voisin);
		}
	}



	/**
	 * Donne la position actuelle (copie)
	 */
	public Position getPos() {
		return new Position(pos);
	}

	/**
	 * Mutateur : change la case courante et met à jour la position
	 * Déclenche mise à jour observateurs
	 */
	public void setCase(Case nouvelleCase) {
		this.caseCourante = nouvelleCase;
		this.pos = nouvelleCase.getPosition();
		this.avertirLesObservers();
	}

	/**
	 * Retourne la case actuelle
	 */
	public Case getCase() {
		return this.caseCourante;
	}
}
