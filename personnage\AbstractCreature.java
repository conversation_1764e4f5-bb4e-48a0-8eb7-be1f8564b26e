package personnage;

import physique.Direction;

public abstract class AbstractCreature extends AbstractPersonnage {
    protected boolean vivant = true;

    public AbstractCreature(String nom) {
        super(nom);
    }

    /**
     * Retourne si la créature est vivante
     */
    public boolean getVivant() {
        return vivant;
    }

    /**
     * Définit si la créature est vivante
     */
    public void setVivant(boolean vivant) {
        this.vivant = vivant;
    }

    /**
     * Déplacement aléatoire de la créature
     */
    public void seDeplacer() {
        Direction dir = new Direction();
        int direction = dir.obtenirDirAlea();
        seDeplacer(direction);
    }
}

