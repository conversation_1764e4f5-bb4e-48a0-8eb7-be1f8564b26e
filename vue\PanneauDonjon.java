package vue;

/**
 * Panneau donjon
 * 
 * Panneau qui dessine le donjon et les éléments du jeu (héros, créatures).
 * 
 * Est un observer des éléments du jeu. Lors d'un changement dans le jeu
 * rafraîchit l'affichage automatiquement.
 * 
 * <AUTHOR> | ETS
 * @version Hiver 2022 - TP2
 */

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import javax.swing.JPanel;

import modele.PlanDeJeu;
import observer.MonObserver;

@SuppressWarnings("serial")
public class PanneauDonjon extends JPanel implements MonObserver {

	private final Dimension taille;
	private final EnginDessinDonjon enginDessinDonjon;
	private final PlanDeJeu planDeJeu = PlanDeJeu.getInstance();

	/**
	 * Constructeur
	 * @param taille, taille de la fenêtre
	 */
	public PanneauDonjon(Dimension taille) {
		this.taille = taille;
		Dimension centre = new Dimension(taille.width / 2, taille.height / 2);
		this.enginDessinDonjon = new EnginDessinDonjon(centre);

		// S'abonner aux notifications du modèle
		planDeJeu.attacherObserver(this);

		setBackground(Color.cyan);
		setPreferredSize(this.taille);
		validate();
		repaint();
	}

	/**
	 * Exécuté à chaque rafraîchissement d'écran.
	 * @param g, référence à l'engin graphique
	 */
	@Override
	protected void paintComponent(Graphics g) {
		super.paintComponent(g); // nettoie automatiquement l'écran
		Graphics2D g2 = (Graphics2D) g;

		// Vérifie que le donjon est bien initialisé
		if (planDeJeu.getDonjon() != null) {
			enginDessinDonjon.dessinerDonjon(g2, planDeJeu.getDonjon());
			enginDessinDonjon.dessinerCreatures(g2, planDeJeu.getCreatures());
			enginDessinDonjon.dessinerJoueur(g2, planDeJeu.getHeros());
		}
	}

	/**
	 * Méthode callback de l'observer : appelée lorsqu’un changement survient.
	 */
	@Override
	public void avertir() {
		repaint(); // déclenche la méthode paintComponent
	}
}
