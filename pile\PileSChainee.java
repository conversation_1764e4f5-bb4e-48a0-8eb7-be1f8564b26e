package pile;

public class <PERSON><PERSON><PERSON><PERSON><PERSON><T> {
    private Noeud<T> sommet;

    private static class Noeud<T> {
        T element;
        Noeud<T> suivant;

        Noeud(T element, Noeud<T> suivant) {
            this.element = element;
            this.suivant = suivant;
        }
    }

    public void empiler(T element) {
        sommet = new Noeud<>(element, sommet);
    }

    public T depiler() {
        if (estVide()) return null;
        T elem = sommet.element;
        sommet = sommet.suivant;
        return elem;
    }

    public T regarder() {
        return (sommet != null) ? sommet.element : null;
    }

    public boolean estVide() {
        return sommet == null;
    }
}