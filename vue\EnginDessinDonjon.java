package vue;

/**
 * Engin de dessin pour le jeu de donjon
 * 
 * L'engin de dessin permet d'afficher le donjon dans une fenêtre graphique.
 * Les objets à dessiner sont passés par paramètre, lors de l'appel de la méthode
 * appropriée.
 * 
 * Voici la liste des services:
 * 	- EnginDessinDonjon, constructeur par paramètre
 *  - dessiner<PERSON><PERSON>eur, dessine un joueur
 *  - dessinerCreatures, dessine toutes les créatures
 *  - dessinerDonjon, dessine le donjon
 * 
 *   <AUTHOR> | ETS
 * @version Hiver 2022 - TP2
 */
 

import java.awt.*;
import java.util.Vector;

import donjon.Case;
import donjon.Configuration;
import donjon.Donjon;
import personnage.AbstractCreature;
import personnage.Araignee;
import personnage.Dragon;
import personnage.Heros;
import personnage.Minotaure;
import physique.Direction;
import physique.Position;

public class EnginDessinDonjon {

	private Dimension centre;
	private static final int LONGUEUR_CASE = 30;

	public EnginDessinDonjon(Dimension centre) {
		this.centre = centre;
	}

	private Position convertirIJaPixel(Position ij) {
		Configuration config = Configuration.getInstance();
		Position hautGauche = new Position(
			centre.height - (int)config.getConfig(Configuration.NB_LIGNES)/2 * LONGUEUR_CASE,
			centre.width - (int)config.getConfig(Configuration.NB_COLONNES)/2 * LONGUEUR_CASE
		);
		ij = ij.clone();
		ij.multiplierPos(LONGUEUR_CASE, LONGUEUR_CASE);
		ij.additionnerPos(hautGauche);
		return ij;
	}

	public void dessinerDonjon(Graphics2D g2, Donjon donjon) {
		Case[][] cases = donjon.getGrille();
		for (int i = 0; i < cases.length; i++) {
			for (int j = 0; j < cases[i].length; j++) {
				dessinerCase(g2, convertirIJaPixel(cases[i][j].getPosition()), cases[i][j]);
			}
		}
	}

	private void dessinerCase(Graphics2D g2, Position pos, Case cetteCase) {
		int yTop = pos.getI() - LONGUEUR_CASE/2;
		int xGauche = pos.getJ() - LONGUEUR_CASE/2;
		int yBas = pos.getI() + LONGUEUR_CASE/2;
		int xDroit = pos.getJ() + LONGUEUR_CASE/2;

		if (cetteCase.getFin()) {
			g2.setColor(Color.BLUE);
			g2.fillRect(xGauche, yTop, LONGUEUR_CASE, LONGUEUR_CASE);
		}

		if (cetteCase.getDecouverte()) {
			g2.setColor(Color.GRAY);
			g2.setStroke(new BasicStroke(7));
			if (cetteCase.getVoisin(Direction.HAUT) == null)
				g2.drawLine(xGauche, yTop, xDroit, yTop);
			if (cetteCase.getVoisin(Direction.BAS) == null)
				g2.drawLine(xGauche, yBas, xDroit, yBas);
			if (cetteCase.getVoisin(Direction.DROITE) == null)
				g2.drawLine(xDroit, yTop, xDroit, yBas);
			if (cetteCase.getVoisin(Direction.GAUCHE) == null)
				g2.drawLine(xGauche, yTop, xGauche, yBas);
		} else {
			g2.setColor(Color.BLACK);
			g2.fillRect(xGauche, yTop, LONGUEUR_CASE, LONGUEUR_CASE);
		}
	}

	public void dessinerCreatures(Graphics2D g2, Vector<AbstractCreature> creatures) {
		for (AbstractCreature creature : creatures) {
			if (creature.getCase().getDecouverte() && creature.getVivant()) {
				dessinerCreature(g2, convertirIJaPixel(creature.getPos()), creature);
			}
		}
	}

	private void dessinerCreature(Graphics2D g2, Position pos, AbstractCreature creature) {
		int x = pos.getJ() - LONGUEUR_CASE/2;
		int y = pos.getI() - LONGUEUR_CASE/2;

		if (creature instanceof Araignee) {
			g2.setColor(Color.CYAN);
		} else if (creature instanceof Dragon) {
			g2.setColor(Color.RED);
		} else if (creature instanceof Minotaure) {
			g2.setColor(Color.GRAY);
		} else {
			g2.setColor(Color.MAGENTA);
		}

		g2.fillOval(x, y, LONGUEUR_CASE, LONGUEUR_CASE);
	}

	public void dessinerJoueur(Graphics2D g2, Heros joueur) {
		Position pos = convertirIJaPixel(joueur.getPos());
		int x = pos.getJ() - LONGUEUR_CASE / 2;
		int y = pos.getI() - LONGUEUR_CASE / 2;
		g2.setColor(Color.YELLOW);
		g2.fillOval(x, y, LONGUEUR_CASE, LONGUEUR_CASE);
	}
}
